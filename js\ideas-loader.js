// Ideas Loader and Display Functions

class IdeasLoader {
    constructor() {
        this.categories = null;
        this.ideas = null;
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.filters = {
            category: '',
            search: '',
            priceRange: ''
        };
        this.init();
    }

    async init() {
        try {
            await this.loadCategories();
            await this.loadIdeas();
            this.renderCategories();
            this.renderIdeas();
            console.log('Ideas Store loaded successfully');
        } catch (error) {
            console.error('Error loading ideas store:', error);
        }
    }

    async loadCategories() {
        try {
            const response = await fetch('data/ideas-categories.json');
            this.categories = await response.json();
        } catch (error) {
            console.error('Error loading categories:', error);
            // Fallback to default categories
            this.categories = this.getDefaultCategories();
        }
    }

    async loadIdeas() {
        // Use the ideas data from ideas-data.js
        if (window.ideasData) {
            this.ideas = window.ideasData;
        } else {
            console.error('Ideas data not found');
            this.ideas = {};
        }
    }

    getDefaultCategories() {
        return {
            business: {
                name: "أفكار تجارية",
                icon: "fas fa-briefcase",
                description: "أفكار مشاريع تجارية مبتكرة ومربحة"
            },
            technology: {
                name: "أفكار تقنية",
                icon: "fas fa-laptop-code",
                description: "أفكار تقنية مبتكرة وحلول رقمية"
            },
            creative: {
                name: "أفكار إبداعية",
                icon: "fas fa-palette",
                description: "أفكار إبداعية وفنية مبتكرة"
            },
            education: {
                name: "أفكار تعليمية",
                icon: "fas fa-graduation-cap",
                description: "أفكار تعليمية وتدريبية مبتكرة"
            }
        };
    }

    renderCategories() {
        const categoriesGrid = document.getElementById('categoriesGrid');
        if (!categoriesGrid || !this.categories) return;

        const categoriesHTML = Object.entries(this.categories).map(([key, category]) => {
            const ideaCount = this.ideas[key] ? this.ideas[key].length : 0;
            
            return `
                <div class="category-card" data-category="${key}">
                    <div class="category-icon floating-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <h3 class="category-title">${category.name}</h3>
                    <p class="category-description">${category.description}</p>
                    <div class="category-stats">
                        <div class="stat-item">
                            <div class="stat-number">${ideaCount}</div>
                            <div class="stat-label">فكرة متاحة</div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="ideasLoader.filterByCategory('${key}')">
                            <i class="fas fa-arrow-left me-2"></i>
                            تصفح
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        categoriesGrid.innerHTML = categoriesHTML;
    }

    renderIdeas() {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid || !this.ideas) return;

        // Get all ideas and apply filters
        let allIdeas = [];
        Object.entries(this.ideas).forEach(([category, ideas]) => {
            ideas.forEach(idea => {
                idea.category = category;
                allIdeas.push(idea);
            });
        });

        // Apply filters
        let filteredIdeas = this.applyFilters(allIdeas);

        // Pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedIdeas = filteredIdeas.slice(startIndex, endIndex);

        const ideasHTML = paginatedIdeas.map(idea => this.renderIdeaCard(idea)).join('');
        productsGrid.innerHTML = ideasHTML;

        // Update load more button
        this.updateLoadMoreButton(filteredIdeas.length);
    }

    renderIdeaCard(idea) {
        const difficultyClass = this.getDifficultyClass(idea.difficulty);
        const stars = '★'.repeat(Math.floor(idea.rating)) + '☆'.repeat(5 - Math.floor(idea.rating));
        
        return `
            <div class="product-card" data-id="${idea.id}">
                <div class="product-header">
                    <div class="difficulty-badge ${difficultyClass}">${idea.difficulty}</div>
                    <div class="product-category">${this.getCategoryName(idea.category)}</div>
                    <h3 class="product-title">${idea.name}</h3>
                    <div class="product-rating">
                        <div class="stars">${stars}</div>
                        <span>(${idea.reviews} تقييم)</span>
                    </div>
                </div>
                <div class="product-body">
                    <p class="product-description">${idea.description}</p>
                    <ul class="product-features">
                        ${idea.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    <div class="product-tags">
                        ${idea.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-clock me-2"></i>
                            <span>${idea.timeToImplement}</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-users me-2"></i>
                            <span>${idea.targetMarket}</span>
                        </div>
                    </div>
                </div>
                <div class="product-footer">
                    <div class="product-price">
                        ${idea.price} <span class="currency">ريال</span>
                    </div>
                    <div class="product-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="ideasLoader.showIdeaDetails('${idea.id}')">
                            <i class="fas fa-eye me-2"></i>
                            مشاهدة التفاصيل
                        </button>
                        <button class="btn btn-primary" onclick="ideasLoader.addToCart('${idea.id}')">
                            <i class="fas fa-cart-plus me-2"></i>
                            إضافة للسلة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    getDifficultyClass(difficulty) {
        const difficultyMap = {
            'سهل': 'difficulty-easy',
            'متوسط': 'difficulty-medium',
            'متقدم': 'difficulty-advanced',
            'خبير': 'difficulty-expert'
        };
        return difficultyMap[difficulty] || 'difficulty-medium';
    }

    getCategoryName(categoryKey) {
        return this.categories[categoryKey]?.name || categoryKey;
    }

    applyFilters(ideas) {
        return ideas.filter(idea => {
            // Category filter
            if (this.filters.category && idea.category !== this.filters.category) {
                return false;
            }

            // Search filter
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const searchableText = `${idea.name} ${idea.description} ${idea.tags.join(' ')}`.toLowerCase();
                if (!searchableText.includes(searchTerm)) {
                    return false;
                }
            }

            // Price filter
            if (this.filters.priceRange) {
                const price = idea.price;
                switch (this.filters.priceRange) {
                    case '0-50':
                        return price <= 50;
                    case '50-100':
                        return price > 50 && price <= 100;
                    case '100-200':
                        return price > 100 && price <= 200;
                    case '200+':
                        return price > 200;
                    default:
                        return true;
                }
            }

            return true;
        });
    }

    filterByCategory(category) {
        this.filters.category = category;
        this.currentPage = 1;
        this.renderIdeas();
        
        // Scroll to products section
        document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
    }

    search(searchTerm) {
        this.filters.search = searchTerm;
        this.currentPage = 1;
        this.renderIdeas();
    }

    filterByPrice(priceRange) {
        this.filters.priceRange = priceRange;
        this.currentPage = 1;
        this.renderIdeas();
    }

    loadMore() {
        this.currentPage++;
        this.renderIdeas();
    }

    updateLoadMoreButton(totalItems) {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (!loadMoreBtn) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        if (this.currentPage >= totalPages) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'block';
        }
    }

    showIdeaDetails(ideaId) {
        // Find the idea
        let idea = null;
        Object.values(this.ideas).forEach(categoryIdeas => {
            const found = categoryIdeas.find(i => i.id === ideaId);
            if (found) idea = found;
        });

        if (idea) {
            this.displayIdeaModal(idea);
        }
    }

    displayIdeaModal(idea) {
        const modalHTML = `
            <div class="modal fade" id="ideaDetailsModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-gradient-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-lightbulb me-2"></i>
                                ${idea.name}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.generateIdeaDetailsContent(idea)}
                        </div>
                        <div class="modal-footer">
                            <div class="d-flex justify-content-between align-items-center w-100">
                                <div class="idea-price-large">
                                    <span class="price-label">السعر:</span>
                                    <span class="price-value">${idea.price} ريال</span>
                                </div>
                                <div class="modal-actions">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-2"></i>
                                        إغلاق
                                    </button>
                                    <button type="button" class="btn btn-success btn-lg" onclick="ideasLoader.addToCartFromModal('${idea.id}')">
                                        <i class="fas fa-cart-plus me-2"></i>
                                        اشتري الآن
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('ideaDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('ideaDetailsModal'));
        modal.show();
    }

    addToCart(ideaId) {
        // Find the idea
        let idea = null;
        Object.values(this.ideas).forEach(categoryIdeas => {
            const found = categoryIdeas.find(i => i.id === ideaId);
            if (found) idea = found;
        });

        if (idea) {
            // Add to cart logic here
            console.log('Adding to cart:', idea);

            // Show notification
            if (window.DatabaseStore && window.DatabaseStore.showNotification) {
                window.DatabaseStore.showNotification(
                    'تمت الإضافة للسلة',
                    `تم إضافة "${idea.name}" إلى سلة التسوق`,
                    'success'
                );
            }
        }
    }

    addToCartFromModal(ideaId) {
        this.addToCart(ideaId);
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('ideaDetailsModal'));
        if (modal) {
            modal.hide();
        }
    }

    generateIdeaDetailsContent(idea) {
        const stars = '★'.repeat(Math.floor(idea.rating)) + '☆'.repeat(5 - Math.floor(idea.rating));
        const difficultyClass = this.getDifficultyClass(idea.difficulty);

        return `
            <div class="idea-details-content">
                <!-- Header Section -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="idea-header">
                            <div class="idea-category-badge">
                                <i class="fas fa-tag me-2"></i>
                                ${this.getCategoryName(idea.category)}
                            </div>
                            <h3 class="idea-title-large">${idea.name}</h3>
                            <div class="idea-rating-large">
                                <div class="stars-large">${stars}</div>
                                <span class="rating-text">${idea.rating}/5 (${idea.reviews} تقييم)</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="idea-difficulty-card">
                            <div class="difficulty-badge-large ${difficultyClass}">
                                <i class="fas fa-chart-line me-2"></i>
                                مستوى الصعوبة: ${idea.difficulty}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description Section -->
                <div class="idea-section mb-4">
                    <h4 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>
                        وصف الفكرة
                    </h4>
                    <p class="idea-description-detailed">${idea.description}</p>
                </div>

                <!-- Key Features Section -->
                <div class="idea-section mb-4">
                    <h4 class="section-title">
                        <i class="fas fa-star me-2"></i>
                        المميزات الرئيسية
                    </h4>
                    <div class="features-grid">
                        ${idea.features.map(feature => `
                            <div class="feature-item-detailed">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>${feature}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Implementation Details -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="info-card">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="info-content">
                                <h5>الوقت المطلوب للتنفيذ</h5>
                                <p>${idea.timeToImplement}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card">
                            <div class="info-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="info-content">
                                <h5>الجمهور المستهدف</h5>
                                <p>${idea.targetMarket}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="info-card investment-card">
                            <div class="info-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="info-content">
                                <h5>الاستثمار المطلوب</h5>
                                <p class="investment-amount">${idea.investmentRequired}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card success-card">
                            <div class="info-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="info-content">
                                <h5>العائد المتوقع</h5>
                                <p class="roi-amount">${idea.expectedROI || 'عائد ممتاز متوقع'}</p>
                            </div>
                        </div>
                    </div>
                </div>

                ${idea.successRate ? `
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="success-rate-card">
                            <div class="success-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="success-content">
                                <h5>معدل النجاح</h5>
                                <p class="success-percentage">${idea.successRate}</p>
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}

                <!-- Tags Section -->
                <div class="idea-section mb-4">
                    <h4 class="section-title">
                        <i class="fas fa-tags me-2"></i>
                        العلامات ذات الصلة
                    </h4>
                    <div class="tags-container">
                        ${idea.tags.map(tag => `
                            <span class="tag-detailed">${tag}</span>
                        `).join('')}
                    </div>
                </div>

                <!-- Success Guarantee -->
                <div class="guarantee-section">
                    <div class="guarantee-card">
                        <div class="guarantee-icon">
                            <i class="fas fa-shield-check"></i>
                        </div>
                        <div class="guarantee-content">
                            <h4>ضمان النجاح</h4>
                            <p>نضمن لك جودة الفكرة وإمكانية تطبيقها. في حالة عدم الرضا، نوفر استرداد كامل خلال 7 أيام من الشراء.</p>
                        </div>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="cta-section text-center">
                    <h4 class="cta-title">جاهز لبدء مشروعك الناجح؟</h4>
                    <p class="cta-subtitle">احصل على هذه الفكرة المبتكرة الآن وابدأ رحلتك نحو النجاح!</p>
                </div>
            </div>
        `;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.ideasLoader = new IdeasLoader();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IdeasLoader;
}
