// Ideas Loader and Display Functions

class IdeasLoader {
    constructor() {
        this.categories = null;
        this.ideas = null;
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.filters = {
            category: '',
            search: '',
            priceRange: ''
        };
        this.init();
    }

    async init() {
        try {
            await this.loadCategories();
            await this.loadIdeas();
            this.renderCategories();
            this.renderIdeas();
            console.log('Ideas Store loaded successfully');
        } catch (error) {
            console.error('Error loading ideas store:', error);
        }
    }

    async loadCategories() {
        try {
            const response = await fetch('data/ideas-categories.json');
            this.categories = await response.json();
        } catch (error) {
            console.error('Error loading categories:', error);
            // Fallback to default categories
            this.categories = this.getDefaultCategories();
        }
    }

    async loadIdeas() {
        // Use the ideas data from ideas-data.js
        if (window.ideasData) {
            this.ideas = window.ideasData;
        } else {
            console.error('Ideas data not found');
            this.ideas = {};
        }
    }

    getDefaultCategories() {
        return {
            business: {
                name: "أفكار تجارية",
                icon: "fas fa-briefcase",
                description: "أفكار مشاريع تجارية مبتكرة ومربحة"
            },
            technology: {
                name: "أفكار تقنية",
                icon: "fas fa-laptop-code",
                description: "أفكار تقنية مبتكرة وحلول رقمية"
            },
            creative: {
                name: "أفكار إبداعية",
                icon: "fas fa-palette",
                description: "أفكار إبداعية وفنية مبتكرة"
            },
            education: {
                name: "أفكار تعليمية",
                icon: "fas fa-graduation-cap",
                description: "أفكار تعليمية وتدريبية مبتكرة"
            }
        };
    }

    renderCategories() {
        const categoriesGrid = document.getElementById('categoriesGrid');
        if (!categoriesGrid || !this.categories) return;

        const categoriesHTML = Object.entries(this.categories).map(([key, category]) => {
            const ideaCount = this.ideas[key] ? this.ideas[key].length : 0;
            
            return `
                <div class="category-card" data-category="${key}">
                    <div class="category-icon floating-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <h3 class="category-title">${category.name}</h3>
                    <p class="category-description">${category.description}</p>
                    <div class="category-stats">
                        <div class="stat-item">
                            <div class="stat-number">${ideaCount}</div>
                            <div class="stat-label">فكرة متاحة</div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" onclick="ideasLoader.filterByCategory('${key}')">
                            <i class="fas fa-arrow-left me-2"></i>
                            تصفح
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        categoriesGrid.innerHTML = categoriesHTML;
    }

    renderIdeas() {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid || !this.ideas) return;

        // Get all ideas and apply filters
        let allIdeas = [];
        Object.entries(this.ideas).forEach(([category, ideas]) => {
            ideas.forEach(idea => {
                idea.category = category;
                allIdeas.push(idea);
            });
        });

        // Apply filters
        let filteredIdeas = this.applyFilters(allIdeas);

        // Pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedIdeas = filteredIdeas.slice(startIndex, endIndex);

        const ideasHTML = paginatedIdeas.map(idea => this.renderIdeaCard(idea)).join('');
        productsGrid.innerHTML = ideasHTML;

        // Update load more button
        this.updateLoadMoreButton(filteredIdeas.length);
    }

    renderIdeaCard(idea) {
        const difficultyClass = this.getDifficultyClass(idea.difficulty);
        const stars = '★'.repeat(Math.floor(idea.rating)) + '☆'.repeat(5 - Math.floor(idea.rating));
        
        return `
            <div class="product-card" data-id="${idea.id}">
                <div class="product-header">
                    <div class="difficulty-badge ${difficultyClass}">${idea.difficulty}</div>
                    <div class="product-category">${this.getCategoryName(idea.category)}</div>
                    <h3 class="product-title">${idea.name}</h3>
                    <div class="product-rating">
                        <div class="stars">${stars}</div>
                        <span>(${idea.reviews} تقييم)</span>
                    </div>
                </div>
                <div class="product-body">
                    <p class="product-description">${idea.description}</p>
                    <ul class="product-features">
                        ${idea.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    <div class="product-tags">
                        ${idea.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-clock me-2"></i>
                            <span>${idea.timeToImplement}</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-users me-2"></i>
                            <span>${idea.targetMarket}</span>
                        </div>
                    </div>
                </div>
                <div class="product-footer">
                    <div class="product-price">
                        ${idea.price} <span class="currency">ريال</span>
                    </div>
                    <button class="btn btn-primary" onclick="ideasLoader.addToCart('${idea.id}')">
                        <i class="fas fa-cart-plus me-2"></i>
                        إضافة للسلة
                    </button>
                </div>
            </div>
        `;
    }

    getDifficultyClass(difficulty) {
        const difficultyMap = {
            'سهل': 'difficulty-easy',
            'متوسط': 'difficulty-medium',
            'متقدم': 'difficulty-advanced',
            'خبير': 'difficulty-expert'
        };
        return difficultyMap[difficulty] || 'difficulty-medium';
    }

    getCategoryName(categoryKey) {
        return this.categories[categoryKey]?.name || categoryKey;
    }

    applyFilters(ideas) {
        return ideas.filter(idea => {
            // Category filter
            if (this.filters.category && idea.category !== this.filters.category) {
                return false;
            }

            // Search filter
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const searchableText = `${idea.name} ${idea.description} ${idea.tags.join(' ')}`.toLowerCase();
                if (!searchableText.includes(searchTerm)) {
                    return false;
                }
            }

            // Price filter
            if (this.filters.priceRange) {
                const price = idea.price;
                switch (this.filters.priceRange) {
                    case '0-50':
                        return price <= 50;
                    case '50-100':
                        return price > 50 && price <= 100;
                    case '100-200':
                        return price > 100 && price <= 200;
                    case '200+':
                        return price > 200;
                    default:
                        return true;
                }
            }

            return true;
        });
    }

    filterByCategory(category) {
        this.filters.category = category;
        this.currentPage = 1;
        this.renderIdeas();
        
        // Scroll to products section
        document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
    }

    search(searchTerm) {
        this.filters.search = searchTerm;
        this.currentPage = 1;
        this.renderIdeas();
    }

    filterByPrice(priceRange) {
        this.filters.priceRange = priceRange;
        this.currentPage = 1;
        this.renderIdeas();
    }

    loadMore() {
        this.currentPage++;
        this.renderIdeas();
    }

    updateLoadMoreButton(totalItems) {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (!loadMoreBtn) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        if (this.currentPage >= totalPages) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'block';
        }
    }

    addToCart(ideaId) {
        // Find the idea
        let idea = null;
        Object.values(this.ideas).forEach(categoryIdeas => {
            const found = categoryIdeas.find(i => i.id === ideaId);
            if (found) idea = found;
        });

        if (idea) {
            // Add to cart logic here
            console.log('Adding to cart:', idea);
            
            // Show notification
            if (window.DatabaseStore && window.DatabaseStore.showNotification) {
                window.DatabaseStore.showNotification(
                    'تمت الإضافة للسلة',
                    `تم إضافة "${idea.name}" إلى سلة التسوق`,
                    'success'
                );
            }
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.ideasLoader = new IdeasLoader();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IdeasLoader;
}
