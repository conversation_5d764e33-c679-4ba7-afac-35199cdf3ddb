// Database Store Cart Management

// Cart data
let cartData = {
    items: [],
    total: 0,
    itemCount: 0
};

// Initialize cart
function initializeCart() {
    loadCartFromStorage();
    updateCartDisplay();
    setupCartEventListeners();
}

// Setup event listeners
function setupCartEventListeners() {
    const cartIcon = document.getElementById('cartIcon');
    if (cartIcon) {
        cartIcon.addEventListener('click', showCartModal);
    }

    const checkoutBtn = document.getElementById('checkoutBtn');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', proceedToCheckout);
    }
}

// Add item to cart
function addToCart(productId) {
    // Try to find in ideas data first
    let product = null;

    if (window.ideasData) {
        Object.values(window.ideasData).forEach(categoryIdeas => {
            const found = categoryIdeas.find(i => i.id === productId);
            if (found) {
                product = {
                    id: found.id,
                    title: found.name,
                    category: found.category,
                    price: found.price,
                    originalPrice: found.price,
                    count: 1, // Ideas don't have count like databases
                    type: 'idea'
                };
            }
        });
    }

    // Fallback to original products data
    if (!product && window.productsData) {
        product = window.productsData.find(p => p.id === productId);
    }

    if (!product) {
        showNotification('خطأ', 'المنتج غير موجود', 'error');
        return;
    }

    // Check if item already exists in cart
    const existingItem = cartData.items.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
        showNotification('تم التحديث', `تم زيادة كمية ${product.title}`, 'success');
    } else {
        const cartItem = {
            id: product.id,
            title: product.title,
            category: product.category,
            price: product.price,
            originalPrice: product.originalPrice,
            count: product.count,
            quantity: 1,
            image: product.image || null
        };
        
        cartData.items.push(cartItem);
        showNotification('تم الإضافة', `تم إضافة ${product.title} إلى السلة`, 'success');
    }

    updateCartData();
    updateCartDisplay();
    saveCartToStorage();
    
    // Add animation to cart icon
    animateCartIcon();
}

// Remove item from cart
function removeFromCart(productId) {
    const itemIndex = cartData.items.findIndex(item => item.id === productId);
    
    if (itemIndex > -1) {
        const item = cartData.items[itemIndex];
        cartData.items.splice(itemIndex, 1);
        
        showNotification('تم الحذف', `تم حذف ${item.title} من السلة`, 'warning');
        
        updateCartData();
        updateCartDisplay();
        saveCartToStorage();
        renderCartModal();
    }
}

// Update item quantity
function updateQuantity(productId, newQuantity) {
    const item = cartData.items.find(item => item.id === productId);
    
    if (!item) return;
    
    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }
    
    item.quantity = newQuantity;
    
    updateCartData();
    updateCartDisplay();
    saveCartToStorage();
    renderCartModal();
}

// Update cart data calculations
function updateCartData() {
    cartData.itemCount = cartData.items.reduce((sum, item) => sum + item.quantity, 0);
    cartData.total = cartData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
}

// Update cart display
function updateCartDisplay() {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = cartData.itemCount;
        
        // Show/hide cart count badge
        if (cartData.itemCount > 0) {
            cartCount.style.display = 'flex';
        } else {
            cartCount.style.display = 'none';
        }
    }
}

// Animate cart icon
function animateCartIcon() {
    const cartIcon = document.getElementById('cartIcon');
    if (cartIcon) {
        cartIcon.style.transform = 'scale(1.2)';
        setTimeout(() => {
            cartIcon.style.transform = 'scale(1)';
        }, 200);
    }
}

// Show cart modal
function showCartModal() {
    renderCartModal();
    const cartModal = new bootstrap.Modal(document.getElementById('cartModal'));
    cartModal.show();
}

// Render cart modal content
function renderCartModal() {
    const cartModalBody = document.getElementById('cartModalBody');
    if (!cartModalBody) return;

    if (cartData.items.length === 0) {
        cartModalBody.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-lightbulb fa-3x text-muted mb-3"></i>
                <h4>السلة فارغة</h4>
                <p class="text-muted">لم تقم بإضافة أي أفكار مبتكرة بعد</p>
                <button class="btn btn-primary" data-bs-dismiss="modal" onclick="scrollToSection('products')">
                    <i class="fas fa-lightbulb me-2"></i>
                    تصفح الأفكار المبتكرة
                </button>
            </div>
        `;
        return;
    }

    let cartHTML = '<div class="cart-items">';
    
    cartData.items.forEach(item => {
        const isIdea = item.type === 'idea';
        const categoryName = isIdea ? getCategoryDisplayName(item.category) : item.category;
        const itemDetails = isIdea ? 'فكرة مبتكرة' : `${formatNumber(item.count)} جهة اتصال`;

        cartHTML += `
            <div class="cart-item">
                <div class="cart-item-info">
                    <h5 class="cart-item-title">${item.title}</h5>
                    <p class="cart-item-category">${categoryName}</p>
                    <div class="cart-item-details">
                        <small class="text-muted">${itemDetails}</small>
                    </div>
                    <div class="cart-item-price">${formatPrice(item.price)}</div>
                </div>
                <div class="cart-item-actions">
                    <div class="quantity-control">
                        <button class="quantity-btn" onclick="updateQuantity('${item.id}', ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" value="${item.quantity}" 
                               onchange="updateQuantity('${item.id}', parseInt(this.value))" min="1">
                        <button class="quantity-btn" onclick="updateQuantity('${item.id}', ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="remove-btn" onclick="removeFromCart('${item.id}')" title="حذف من السلة">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    
    cartHTML += '</div>';
    
    // Add cart summary
    const subtotal = cartData.total;
    const tax = subtotal * 0.15; // 15% VAT
    const total = subtotal + tax;
    
    cartHTML += `
        <div class="cart-summary">
            <div class="summary-row">
                <span>المجموع الفرعي:</span>
                <span>${formatPrice(subtotal)}</span>
            </div>
            <div class="summary-row">
                <span>ضريبة القيمة المضافة (15%):</span>
                <span>${formatPrice(tax)}</span>
            </div>
            <div class="summary-row total">
                <span>المجموع الكلي:</span>
                <span>${formatPrice(total)}</span>
            </div>
        </div>
    `;
    
    cartModalBody.innerHTML = cartHTML;
}

// Proceed to checkout
function proceedToCheckout() {
    if (cartData.items.length === 0) {
        showNotification('تنبيه', 'السلة فارغة. أضف منتجات أولاً', 'warning');
        return;
    }

    // Create checkout progress
    const checkoutContainer = document.createElement('div');
    checkoutContainer.id = 'checkoutContainer';
    document.body.appendChild(checkoutContainer);

    const progressBar = createProgressBar(checkoutContainer, 'جاري معالجة الطلب...');

    // Simulate checkout process
    let progress = 0;
    const steps = [
        { progress: 20, status: 'التحقق من البيانات...' },
        { progress: 40, status: 'معالجة الدفع...' },
        { progress: 60, status: 'تأكيد الطلب...' },
        { progress: 80, status: 'إعداد الملفات...' },
        { progress: 100, status: 'تم الانتهاء بنجاح!' }
    ];

    let currentStep = 0;
    const interval = setInterval(() => {
        if (currentStep < steps.length) {
            const step = steps[currentStep];
            progressBar.updateProgress(step.progress, step.status);
            currentStep++;
        } else {
            clearInterval(interval);
            progressBar.complete();
            
            // Clear cart after successful checkout
            setTimeout(() => {
                clearCart();

                // Check if cart contains ideas
                const hasIdeas = cartData.items.some(item => item.type === 'idea');
                const message = hasIdeas
                    ? 'شكراً لك! سيتم إرسال الأفكار المبتكرة إلى بريدك الإلكتروني خلال دقائق.'
                    : 'شكراً لك! سيتم إرسال قواعد البيانات إلى بريدك الإلكتروني خلال دقائق.';

                showNotification(
                    'تم الشراء بنجاح',
                    message,
                    'success'
                );
                
                // Close modal and remove checkout container
                const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
                if (cartModal) cartModal.hide();
                
                setTimeout(() => {
                    checkoutContainer.remove();
                }, 3000);
            }, 2000);
        }
    }, 1000);
}

// Clear cart
function clearCart() {
    cartData.items = [];
    cartData.total = 0;
    cartData.itemCount = 0;
    
    updateCartDisplay();
    saveCartToStorage();
    renderCartModal();
}

// Save cart to localStorage
function saveCartToStorage() {
    try {
        localStorage.setItem('databaseStoreCart', JSON.stringify(cartData));
    } catch (error) {
        console.error('Error saving cart to storage:', error);
    }
}

// Load cart from localStorage
function loadCartFromStorage() {
    try {
        const savedCart = localStorage.getItem('databaseStoreCart');
        if (savedCart) {
            cartData = JSON.parse(savedCart);
            updateCartData(); // Recalculate in case of data inconsistency
        }
    } catch (error) {
        console.error('Error loading cart from storage:', error);
        cartData = { items: [], total: 0, itemCount: 0 };
    }
}

// Quick add to cart with quantity
function quickAddToCart(productId, quantity = 1) {
    const product = productsData.find(p => p.id === productId);
    if (!product) return;

    const existingItem = cartData.items.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        const cartItem = {
            id: product.id,
            title: product.title,
            category: product.category,
            price: product.price,
            originalPrice: product.originalPrice,
            count: product.count,
            quantity: quantity,
            image: product.image || null
        };
        
        cartData.items.push(cartItem);
    }

    updateCartData();
    updateCartDisplay();
    saveCartToStorage();
    
    showNotification(
        'تم الإضافة',
        `تم إضافة ${quantity} من ${product.title}`,
        'success'
    );
}

// Get cart summary
function getCartSummary() {
    const subtotal = cartData.total;
    const tax = subtotal * 0.15;
    const total = subtotal + tax;
    
    return {
        itemCount: cartData.itemCount,
        subtotal: subtotal,
        tax: tax,
        total: total,
        items: cartData.items
    };
}

// Apply discount code
function applyDiscountCode(code) {
    const discountCodes = {
        'WELCOME10': { type: 'percentage', value: 10, description: 'خصم 10% للعملاء الجدد' },
        'SAVE50': { type: 'fixed', value: 50, description: 'خصم 50 ريال' },
        'BULK20': { type: 'percentage', value: 20, description: 'خصم 20% للطلبات الكبيرة' }
    };
    
    const discount = discountCodes[code.toUpperCase()];
    
    if (!discount) {
        showNotification('كود خاطئ', 'كود الخصم غير صحيح', 'error');
        return false;
    }
    
    let discountAmount = 0;
    if (discount.type === 'percentage') {
        discountAmount = cartData.total * (discount.value / 100);
    } else {
        discountAmount = discount.value;
    }
    
    showNotification(
        'تم تطبيق الخصم',
        `${discount.description} - وفرت ${formatPrice(discountAmount)}`,
        'success'
    );
    
    return {
        code: code,
        amount: discountAmount,
        description: discount.description
    };
}

// Get category display name for ideas
function getCategoryDisplayName(categoryKey) {
    const categoryNames = {
        'business': 'أفكار تجارية',
        'technology': 'أفكار تقنية',
        'creative': 'أفكار إبداعية',
        'education': 'أفكار تعليمية',
        'marketing': 'أفكار تسويقية',
        'social': 'أفكار اجتماعية',
        'entertainment': 'أفكار ترفيهية',
        'health': 'أفكار صحية'
    };

    return categoryNames[categoryKey] || categoryKey;
}

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCart();
});

// Export cart functions
window.DatabaseCart = {
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    showCartModal,
    getCartSummary,
    quickAddToCart,
    applyDiscountCode,
    cartData
};
