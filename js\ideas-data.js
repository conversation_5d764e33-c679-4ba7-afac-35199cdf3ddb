// Ideas Store Data
const ideasData = {
    business: [
        {
            id: 'startup-food-delivery',
            name: 'منصة توصيل الطعام المحلي',
            category: 'business',
            subcategory: 'startup-ideas',
            price: 49,
            rating: 4.8,
            reviews: 156,
            description: 'فكرة مبتكرة لمنصة توصيل طعام تركز على المطاعم المحلية الصغيرة مع نظام تقييم متقدم وخدمة عملاء مميزة. تستهدف الأحياء السكنية والمدن الصغيرة التي لا تغطيها المنصات الكبيرة.',
            features: [
                'نموذج عمل مفصل مع 3 مصادر دخل',
                'دراسة السوق شاملة لـ 15 مدينة',
                'خطة التسويق الرقمي والتقليدي',
                'التحليل المالي مع توقعات 3 سنوات',
                'استراتيجية جذب المطاعم والعملاء',
                'نظام تقييم ذكي للجودة والسرعة',
                'خطة التوسع التدريجي',
                'نماذج العقود والاتفاقيات'
            ],
            tags: ['توصيل', 'طعام', 'تطبيق', 'محلي', 'ربحي'],
            difficulty: 'متوسط',
            timeToImplement: '3-6 أشهر',
            targetMarket: 'المدن الصغيرة والمتوسطة (50-200 ألف نسمة)',
            investmentRequired: '50,000 - 100,000 ريال',
            expectedROI: '200-300% خلال السنة الثانية',
            successRate: '85% نجاح في الأسواق المشابهة'
        },
        {
            id: 'eco-friendly-products',
            name: 'متجر المنتجات الصديقة للبيئة',
            category: 'business',
            subcategory: 'ecommerce-ideas',
            price: 39,
            rating: 4.7,
            reviews: 89,
            description: 'فكرة متجر إلكتروني متخصص في بيع المنتجات الصديقة للبيئة والمستدامة',
            features: ['كتالوج المنتجات', 'استراتيجية التسويق الأخضر', 'شراكات الموردين', 'خطة النمو'],
            tags: ['بيئة', 'استدامة', 'تجارة إلكترونية', 'أخضر'],
            difficulty: 'سهل',
            timeToImplement: '2-4 أشهر',
            targetMarket: 'المهتمين بالبيئة',
            investmentRequired: '20,000 - 50,000 ريال'
        },
        {
            id: 'virtual-fitness-coaching',
            name: 'منصة التدريب الرياضي الافتراضي',
            category: 'business',
            subcategory: 'startup-ideas',
            price: 59,
            rating: 4.9,
            reviews: 203,
            description: 'منصة تدريب رياضي افتراضي مع مدربين شخصيين وبرامج مخصصة. تستفيد من الطلب المتزايد على اللياقة البدنية المنزلية والتدريب عن بُعد.',
            features: [
                'نموذج الاشتراك الشهري والسنوي',
                'تقنيات التدريب بالذكاء الاصطناعي',
                'نظام المتابعة والتقييم الذكي',
                'خطة التوسع للأسواق العربية',
                'شراكات مع المدربين المعتمدين',
                'تطبيق موبايل متقدم',
                'نظام دفع آمن ومرن',
                'برامج تغذية مخصصة'
            ],
            tags: ['رياضة', 'صحة', 'افتراضي', 'تدريب', 'اشتراكات'],
            difficulty: 'متقدم',
            timeToImplement: '6-12 شهر',
            targetMarket: 'المهتمين باللياقة البدنية (18-45 سنة)',
            investmentRequired: '100,000 - 200,000 ريال',
            expectedROI: '400-600% خلال 18 شهر',
            successRate: '78% نجاح في الأسواق المتقدمة'
        },
        {
            id: 'smart-grocery-delivery',
            name: 'تطبيق توصيل البقالة الذكي',
            category: 'business',
            subcategory: 'startup-ideas',
            price: 55,
            rating: 4.7,
            reviews: 189,
            description: 'تطبيق توصيل بقالة ذكي يتنبأ باحتياجات العملاء ويقدم توصيات شخصية. يركز على السرعة والجودة مع ضمان الطازجية.',
            features: [
                'خوارزمية التنبؤ بالاحتياجات',
                'شراكات مع البقالات المحلية',
                'نظام إدارة المخزون الذكي',
                'خدمة التوصيل السريع (30 دقيقة)',
                'برنامج ولاء العملاء',
                'نظام تتبع الطلبات المباشر',
                'ضمان الطازجية والجودة',
                'خيارات دفع متعددة'
            ],
            tags: ['بقالة', 'توصيل', 'ذكي', 'سريع', 'محلي'],
            difficulty: 'متوسط',
            timeToImplement: '4-8 أشهر',
            targetMarket: 'الأسر والعاملين المشغولين',
            investmentRequired: '75,000 - 150,000 ريال',
            expectedROI: '250-400% خلال السنة الثانية',
            successRate: '82% نجاح في المدن الكبيرة'
        }
    ],
    technology: [
        {
            id: 'smart-home-security',
            name: 'نظام أمان المنزل الذكي',
            category: 'technology',
            subcategory: 'iot-ideas',
            price: 79,
            rating: 4.8,
            reviews: 134,
            description: 'نظام أمان ذكي متكامل للمنازل باستخدام إنترنت الأشياء والذكاء الاصطناعي',
            features: ['مخططات النظام', 'قائمة الأجهزة', 'البرمجيات المطلوبة', 'خطة التطوير'],
            tags: ['أمان', 'منزل ذكي', 'IoT', 'ذكاء اصطناعي'],
            difficulty: 'متقدم',
            timeToImplement: '8-12 شهر',
            targetMarket: 'أصحاب المنازل',
            investmentRequired: '150,000 - 300,000 ريال'
        },
        {
            id: 'ai-language-tutor',
            name: 'مدرس اللغة بالذكاء الاصطناعي',
            category: 'technology',
            subcategory: 'ai-solutions',
            price: 69,
            rating: 4.9,
            reviews: 178,
            description: 'تطبيق تعليم لغات باستخدام الذكاء الاصطناعي مع محادثات تفاعلية',
            features: ['خوارزميات التعلم', 'واجهة المستخدم', 'قاعدة البيانات', 'نظام التقييم'],
            tags: ['تعليم', 'لغات', 'ذكاء اصطناعي', 'تطبيق'],
            difficulty: 'متقدم',
            timeToImplement: '6-10 أشهر',
            targetMarket: 'متعلمي اللغات',
            investmentRequired: '80,000 - 150,000 ريال'
        },
        {
            id: 'blockchain-voting',
            name: 'نظام التصويت بالبلوك تشين',
            category: 'technology',
            subcategory: 'app-ideas',
            price: 89,
            rating: 4.7,
            reviews: 92,
            description: 'نظام تصويت آمن وشفاف باستخدام تقنية البلوك تشين',
            features: ['بنية البلوك تشين', 'واجهة التصويت', 'نظام الأمان', 'التقارير والتحليل'],
            tags: ['بلوك تشين', 'تصويت', 'أمان', 'شفافية'],
            difficulty: 'خبير',
            timeToImplement: '12-18 شهر',
            targetMarket: 'المؤسسات والحكومات',
            investmentRequired: '200,000 - 500,000 ريال'
        }
    ],
    creative: [
        {
            id: 'interactive-storytelling',
            name: 'منصة القصص التفاعلية',
            category: 'creative',
            subcategory: 'content-creation',
            price: 45,
            rating: 4.6,
            reviews: 167,
            description: 'منصة لإنشاء ومشاركة القصص التفاعلية مع عناصر الوسائط المتعددة',
            features: ['أدوات الإنشاء', 'مكتبة الوسائط', 'نظام النشر', 'مجتمع المبدعين'],
            tags: ['قصص', 'تفاعلي', 'إبداع', 'وسائط متعددة'],
            difficulty: 'متوسط',
            timeToImplement: '4-8 أشهر',
            targetMarket: 'الكتاب والمبدعين',
            investmentRequired: '60,000 - 120,000 ريال'
        },
        {
            id: 'ar-art-gallery',
            name: 'معرض الفن بالواقع المعزز',
            category: 'creative',
            subcategory: 'design-concepts',
            price: 55,
            rating: 4.8,
            reviews: 143,
            description: 'معرض فني افتراضي باستخدام تقنية الواقع المعزز لعرض الأعمال الفنية',
            features: ['تقنية AR', 'كتالوج الأعمال', 'تجربة المستخدم', 'نظام المبيعات'],
            tags: ['فن', 'واقع معزز', 'معرض', 'افتراضي'],
            difficulty: 'متقدم',
            timeToImplement: '6-10 أشهر',
            targetMarket: 'الفنانين وعشاق الفن',
            investmentRequired: '100,000 - 200,000 ريال'
        }
    ],
    education: [
        {
            id: 'micro-learning-platform',
            name: 'منصة التعلم المصغر',
            category: 'education',
            subcategory: 'online-courses',
            price: 35,
            rating: 4.7,
            reviews: 234,
            description: 'منصة تعليمية تركز على الدروس القصيرة والتعلم السريع',
            features: ['محتوى مصغر', 'نظام التتبع', 'شهادات إنجاز', 'تطبيق موبايل'],
            tags: ['تعليم', 'مصغر', 'سريع', 'منصة'],
            difficulty: 'متوسط',
            timeToImplement: '3-6 أشهر',
            targetMarket: 'المهنيين المشغولين',
            investmentRequired: '40,000 - 80,000 ريال'
        },
        {
            id: 'vr-history-lessons',
            name: 'دروس التاريخ بالواقع الافتراضي',
            category: 'education',
            subcategory: 'educational-apps',
            price: 65,
            rating: 4.9,
            reviews: 189,
            description: 'تطبيق تعليمي يستخدم الواقع الافتراضي لتدريس التاريخ بطريقة تفاعلية',
            features: ['بيئات VR', 'محتوى تاريخي', 'تفاعل ثلاثي الأبعاد', 'اختبارات تفاعلية'],
            tags: ['تاريخ', 'واقع افتراضي', 'تعليم', 'تفاعلي'],
            difficulty: 'متقدم',
            timeToImplement: '8-12 شهر',
            targetMarket: 'المدارس والطلاب',
            investmentRequired: '120,000 - 250,000 ريال'
        }
    ]
};

// Export the data
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ideasData;
} else {
    window.ideasData = ideasData;
}
