/* Ideas Store Specific Styles */

/* Enhanced gradient backgrounds for ideas theme */
.hero-section {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed, #6366f1);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle fill="rgba(255,255,255,0.1)" cx="200" cy="200" r="100"/><circle fill="rgba(255,255,255,0.05)" cx="800" cy="300" r="150"/><circle fill="rgba(255,255,255,0.08)" cx="600" cy="700" r="120"/></svg>');
    background-size: cover;
}

/* Ideas category cards */
.category-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

.category-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.category-description {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-500);
}

/* Ideas product cards */
.product-card {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.product-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    position: relative;
}

.product-category {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    color: var(--accent-color);
}

.product-body {
    padding: 1.5rem;
}

.product-description {
    color: var(--gray-600);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.product-features {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.product-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.product-features li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
}

.product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.tag {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.product-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.product-price .currency {
    font-size: 1rem;
    color: var(--gray-500);
}

/* Difficulty badges */
.difficulty-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy {
    background: var(--success-color);
    color: white;
}

.difficulty-medium {
    background: var(--warning-color);
    color: white;
}

.difficulty-advanced {
    background: var(--danger-color);
    color: white;
}

.difficulty-expert {
    background: var(--dark-color);
    color: white;
}

/* Ideas specific animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-icon {
    animation: float 3s ease-in-out infinite;
}

/* Enhanced search section for ideas */
.search-section {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.search-card {
    background: white;
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
}

.search-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius-xl);
    z-index: -1;
}

/* Ideas specific trust badges */
.trust-badge {
    background: linear-gradient(135deg, white, #f8fafc);
    border: 2px solid var(--gray-200);
    transition: all 0.3s ease;
}

.trust-badge:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.trust-badge i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Product meta information */
.product-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.meta-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.meta-item i {
    color: var(--primary-color);
    width: 16px;
}

/* Enhanced button styles for ideas */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-title .highlight {
        font-size: 1.75rem;
    }

    .category-card {
        padding: 1.5rem;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .product-card {
        margin-bottom: 1.5rem;
    }

    .product-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .product-meta {
        padding: 0.75rem;
    }

    .meta-item {
        font-size: 0.8rem;
    }
}
