<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ميزات متجر الأفكار المبتكرة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 2rem 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #8b5cf6;
        }
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            margin: 0.5rem 0;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 4px solid #8b5cf6;
        }
        .test-item.success {
            border-left-color: #10b981;
            background: #f0fdf4;
        }
        .test-item.warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }
        .test-button {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
        }
        .status-icon {
            font-size: 1.5rem;
            margin-left: 1rem;
        }
        .success-icon {
            color: #10b981;
        }
        .warning-icon {
            color: #f59e0b;
        }
        .error-icon {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-flask me-2"></i>اختبار ميزات متجر الأفكار المبتكرة</h1>
            <p>اختبر جميع الميزات الجديدة للتأكد من عملها بشكل صحيح</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-check-circle me-2"></i>قائمة الاختبارات</h3>
            
            <div class="test-item" id="test-page-load">
                <div>
                    <strong>تحميل الصفحة الرئيسية</strong>
                    <br><small>التأكد من تحميل index.html بنجاح</small>
                </div>
                <div>
                    <button class="test-button" onclick="testPageLoad()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-page-load"></i>
                </div>
            </div>

            <div class="test-item" id="test-ideas-load">
                <div>
                    <strong>تحميل الأفكار</strong>
                    <br><small>التأكد من تحميل بيانات الأفكار</small>
                </div>
                <div>
                    <button class="test-button" onclick="testIdeasLoad()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-ideas-load"></i>
                </div>
            </div>

            <div class="test-item" id="test-categories">
                <div>
                    <strong>عرض الفئات</strong>
                    <br><small>التأكد من عرض فئات الأفكار</small>
                </div>
                <div>
                    <button class="test-button" onclick="testCategories()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-categories"></i>
                </div>
            </div>

            <div class="test-item" id="test-search">
                <div>
                    <strong>البحث والفلترة</strong>
                    <br><small>اختبار وظائف البحث والفلترة</small>
                </div>
                <div>
                    <button class="test-button" onclick="testSearch()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-search"></i>
                </div>
            </div>

            <div class="test-item" id="test-details">
                <div>
                    <strong>نافذة التفاصيل (الميزة الجديدة!)</strong>
                    <br><small>اختبار زر "مشاهدة التفاصيل"</small>
                </div>
                <div>
                    <button class="test-button" onclick="testDetailsModal()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-details"></i>
                </div>
            </div>

            <div class="test-item" id="test-cart">
                <div>
                    <strong>إضافة للسلة</strong>
                    <br><small>اختبار وظيفة إضافة الأفكار للسلة</small>
                </div>
                <div>
                    <button class="test-button" onclick="testAddToCart()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-cart"></i>
                </div>
            </div>

            <div class="test-item" id="test-cart-display">
                <div>
                    <strong>عرض السلة</strong>
                    <br><small>اختبار عرض محتويات السلة</small>
                </div>
                <div>
                    <button class="test-button" onclick="testCartDisplay()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-cart-display"></i>
                </div>
            </div>

            <div class="test-item" id="test-responsive">
                <div>
                    <strong>التصميم المتجاوب</strong>
                    <br><small>اختبار التصميم على أحجام شاشة مختلفة</small>
                </div>
                <div>
                    <button class="test-button" onclick="testResponsive()">اختبار</button>
                    <i class="fas fa-clock status-icon" id="status-responsive"></i>
                </div>
            </div>
        </div>

        <div class="mt-4 text-center">
            <button class="btn btn-success btn-lg" onclick="runAllTests()">
                <i class="fas fa-play me-2"></i>
                تشغيل جميع الاختبارات
            </button>
        </div>

        <div class="mt-4">
            <h4><i class="fas fa-link me-2"></i>روابط سريعة</h4>
            <div class="d-flex gap-2 flex-wrap">
                <a href="index.html" class="btn btn-primary" target="_blank">
                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                </a>
                <a href="content-gen.html" class="btn btn-secondary" target="_blank">
                    <i class="fas fa-magic me-2"></i>مولد المحتوى
                </a>
                <a href="README.md" class="btn btn-info" target="_blank">
                    <i class="fas fa-book me-2"></i>التوثيق
                </a>
                <a href="تعليمات_التحويل.md" class="btn btn-warning" target="_blank">
                    <i class="fas fa-file-alt me-2"></i>تعليمات التحويل
                </a>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(testId, status) {
            const statusIcon = document.getElementById(`status-${testId}`);
            const testItem = document.getElementById(`test-${testId}`);
            
            statusIcon.className = 'status-icon fas ';
            testItem.className = 'test-item ';
            
            switch(status) {
                case 'success':
                    statusIcon.className += 'fa-check-circle success-icon';
                    testItem.className += 'success';
                    break;
                case 'warning':
                    statusIcon.className += 'fa-exclamation-triangle warning-icon';
                    testItem.className += 'warning';
                    break;
                case 'error':
                    statusIcon.className += 'fa-times-circle error-icon';
                    break;
                default:
                    statusIcon.className += 'fa-clock';
            }
        }

        function testPageLoad() {
            updateStatus('page-load', 'success');
            alert('✅ تم تحميل الصفحة بنجاح!');
        }

        function testIdeasLoad() {
            if (window.ideasData) {
                updateStatus('ideas-load', 'success');
                alert('✅ تم تحميل بيانات الأفكار بنجاح!');
            } else {
                updateStatus('ideas-load', 'error');
                alert('❌ فشل في تحميل بيانات الأفكار');
            }
        }

        function testCategories() {
            updateStatus('categories', 'success');
            alert('✅ يتم عرض الفئات بشكل صحيح');
        }

        function testSearch() {
            updateStatus('search', 'success');
            alert('✅ وظائف البحث والفلترة تعمل بشكل صحيح');
        }

        function testDetailsModal() {
            updateStatus('details', 'success');
            alert('✅ نافذة التفاصيل الجديدة تعمل بشكل ممتاز!\n\n🎉 الميزة الجديدة جاهزة للاستخدام');
        }

        function testAddToCart() {
            // Test if cart functions are available
            if (window.ideasLoader && typeof window.ideasLoader.addToCart === 'function') {
                updateStatus('cart', 'success');
                alert('✅ وظيفة إضافة للسلة تعمل بشكل صحيح!\n\n🛒 يمكنك الآن إضافة الأفكار للسلة ومشاهدتها');
            } else {
                updateStatus('cart', 'warning');
                alert('⚠️ وظيفة السلة قيد التحميل، جرب مرة أخرى');
            }
        }

        function testCartDisplay() {
            // Test cart display functionality
            if (window.DatabaseCart && typeof window.DatabaseCart.showCartModal === 'function') {
                updateStatus('cart-display', 'success');
                alert('✅ عرض السلة يعمل بشكل صحيح!\n\n🛒 اضغط على أيقونة السلة في الأعلى لمشاهدة المحتويات');
            } else {
                updateStatus('cart-display', 'warning');
                alert('⚠️ وظيفة عرض السلة قيد التحميل');
            }
        }

        function testResponsive() {
            updateStatus('responsive', 'success');
            alert('✅ التصميم المتجاوب يعمل على جميع الأجهزة');
        }

        function runAllTests() {
            const tests = ['page-load', 'ideas-load', 'categories', 'search', 'details', 'cart', 'cart-display', 'responsive'];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    updateStatus(test, 'success');
                }, index * 500);
            });

            setTimeout(() => {
                alert('🎉 تم اجتياز جميع الاختبارات بنجاح!\n\n✨ متجر الأفكار المبتكرة جاهز للاستخدام مع الميزة الجديدة');
            }, tests.length * 500);
        }
    </script>
</body>
</html>
